<?php

use Illuminate\Support\Facades\Route;
use App\Models\Invoice;
use App\Services\InvoiceService;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Landing page
Route::get('/', function () {
    $packages = App\Models\Package::where('is_active', true)->get();
    return view('welcome', compact('packages'));
});

// Payment Report Routes
Route::get('/payment-report/print', [App\Http\Controllers\PaymentReportController::class, 'print'])
    ->name('payment-report.print');

// Public Registration Routes
Route::get('/daftar', [App\Http\Controllers\PublicRegistrationController::class, 'showRegistrationForm'])
    ->name('registration.form');
Route::post('/daftar', [App\Http\Controllers\PublicRegistrationController::class, 'register'])
    ->name('registration.store');
Route::get('/daftar/berhasil', [App\Http\Controllers\PublicRegistrationController::class, 'success'])
    ->name('registration.success');

// Service Application Routes
Route::get('/layanan/ajukan', [App\Http\Controllers\PublicRegistrationController::class, 'showServiceForm'])
    ->name('service.application.form');
Route::post('/layanan/ajukan', [App\Http\Controllers\PublicRegistrationController::class, 'applyService'])
    ->name('service.application.store');
Route::get('/layanan/berhasil', [App\Http\Controllers\PublicRegistrationController::class, 'serviceSuccess'])
    ->name('service.application.success');



// Dashboard routes
Route::get('/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');
Route::get('/api/dashboard/stats', [App\Http\Controllers\DashboardController::class, 'getStats'])->name('dashboard.stats');

// Login route for Filament logout redirect (required by Filament)
Route::get('/login', function () {
    return view('auth.login');
})->name('login');







// Invoice PDF routes
Route::get('/invoice/{invoice}/pdf', function (Invoice $invoice) {
    $invoiceService = new InvoiceService();
    return $invoiceService->streamPDF($invoice);
})->name('invoice.pdf');

Route::get('/invoice/{invoice}/download', function (Invoice $invoice) {
    $invoiceService = new InvoiceService();
    return $invoiceService->downloadPDF($invoice);
})->name('invoice.download');
