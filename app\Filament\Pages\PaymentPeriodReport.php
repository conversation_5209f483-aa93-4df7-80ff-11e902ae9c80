<?php

namespace App\Filament\Pages;

use App\Models\Payment;
use Filament\Pages\Page;
use Carbon\Carbon;

class PaymentPeriodReport extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';

    protected static string $view = 'filament.pages.payment-period-report';

    protected static ?string $navigationLabel = 'Laporan Periode';

    protected static ?string $title = 'Laporan Pembayaran Per Periode';

    protected static ?int $navigationSort = 10;

    public function getPaymentsByPeriod()
    {
        $payments = Payment::with(['service.customer', 'invoice.customer'])
            ->where('status', 'completed')
            ->whereNotNull('payment_date')
            ->orderBy('payment_date', 'desc')
            ->get();

        $groupedPayments = [];

        foreach ($payments as $payment) {
            $period = Carbon::parse($payment->payment_date)->locale('id')->isoFormat('MMMM YYYY');

            if (!isset($groupedPayments[$period])) {
                $groupedPayments[$period] = [
                    'period' => $period,
                    'payments' => [],
                    'total' => 0,
                    'count' => 0
                ];
            }

            $groupedPayments[$period]['payments'][] = $payment;
            $groupedPayments[$period]['total'] += $payment->amount;
            $groupedPayments[$period]['count']++;
        }

        return $groupedPayments;
    }
}
