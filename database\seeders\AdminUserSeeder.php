<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Delete existing admin users
        User::whereIn('email', ['<EMAIL>', '<EMAIL>'])->delete();

        // Create new admin user
        User::create([
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);

        $this->command->info('Admin user created successfully!');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: password');
    }
}
